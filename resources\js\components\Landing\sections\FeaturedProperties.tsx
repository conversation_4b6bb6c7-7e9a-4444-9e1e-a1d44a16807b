import React, { useState } from 'react';
import {
  Star,
  MapPin,
  Wifi,
  Shield,
  Eye,
  Heart,
  MessageCircle,
  ChevronLeft,
  ChevronRight,
  SlidersHorizontal
} from 'lucide-react';
import { KostProperty } from '@/types';
import AvailabilityIndicator from '@/components/ui/AvailabilityIndicator';

// Mock data for featured properties (will be replaced with API call)
const mockFeaturedProperties: KostProperty[] = [
  {
    id: '1',
    title: 'Kost Eksklusif Dekat UI Depok',
    description: 'Kost modern dengan fasilitas lengkap, lokasi strategis dekat Universitas Indonesia',
    price_monthly: 2500000,
    property_type: 'campur',
    room_type: 'single',
    available_rooms: 3,
    total_rooms: 20,
    rating: 4.8,
    review_count: 124,
    is_featured: true,
    is_verified: true,
    images: [
      {
        id: '1',
        url: 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800',
        alt: 'Kamar kost modern',
        is_primary: true,
        order: 1
      },
      {
        id: '2',
        url: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800',
        alt: 'Ruang bersama',
        is_primary: false,
        order: 2
      }
    ],
    amenities: [
      { id: '1', name: 'WiFi', icon: 'Wifi', category: 'connectivity', is_popular: true },
      { id: '2', name: 'Parkir', icon: 'Car', category: 'basic', is_popular: true },
      { id: '3', name: 'Keamanan 24 Jam', icon: 'Shield', category: 'security', is_popular: true }
    ],
    location: {
      id: '1',
      address: 'Jl. Margonda Raya No. 123',
      district: 'Beji',
      city: 'Depok',
      province: 'Jawa Barat',
      postal_code: '16424',
      latitude: -6.3728,
      longitude: 106.8317,
      nearby_landmarks: ['Universitas Indonesia', 'Stasiun UI', 'Mall Depok']
    },
    owner: {
      id: '1',
      name: 'Ibu Sari',
      phone: '+6281234567890',
      response_rate: 95,
      response_time: '< 1 jam'
    },
    rules: ['Tidak merokok', 'Tidak membawa tamu menginap'],
    facilities: ['AC', 'Kamar Mandi Dalam', 'Lemari', 'Kasur'],
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  },
  {
    id: '2',
    title: 'Kost Putri Nyaman Jakarta Selatan',
    description: 'Kost khusus putri dengan keamanan terjamin dan fasilitas premium',
    price_monthly: 1800000,
    property_type: 'putri',
    room_type: 'single',
    available_rooms: 5,
    total_rooms: 15,
    rating: 4.6,
    review_count: 89,
    is_featured: true,
    is_verified: true,
    images: [
      {
        id: '3',
        url: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800',
        alt: 'Kamar kost putri',
        is_primary: true,
        order: 1
      }
    ],
    amenities: [
      { id: '1', name: 'WiFi', icon: 'Wifi', category: 'connectivity', is_popular: true },
      { id: '4', name: 'Dapur Bersama', icon: 'Users', category: 'basic', is_popular: true }
    ],
    location: {
      id: '2',
      address: 'Jl. Kemang Raya No. 456',
      district: 'Kemang',
      city: 'Jakarta Selatan',
      province: 'DKI Jakarta',
      postal_code: '12560',
      latitude: -6.2615,
      longitude: 106.8106,
      nearby_landmarks: ['Mall Kemang Village', 'Stasiun MRT Cipete Raya']
    },
    owner: {
      id: '2',
      name: 'Pak Budi',
      phone: '+6281234567891',
      response_rate: 92,
      response_time: '< 2 jam'
    },
    rules: ['Khusus putri', 'Jam malam 22:00'],
    facilities: ['AC', 'Kamar Mandi Dalam', 'WiFi', 'Lemari'],
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  }
];

interface FeaturedPropertiesProps {
  properties?: KostProperty[];
  loading?: boolean;
  onPropertyClick?: (property: KostProperty) => void;
  onContactOwner?: (property: KostProperty) => void;
}

const FeaturedProperties: React.FC<FeaturedPropertiesProps> = ({
  properties = mockFeaturedProperties,
  loading = false,
  onPropertyClick,
  onContactOwner
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState<Record<string, number>>({});
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [showFilters, setShowFilters] = useState(false);

  // Handle image navigation
  const handleImageNavigation = (propertyId: string, direction: 'prev' | 'next', totalImages: number) => {
    setCurrentImageIndex(prev => {
      const current = prev[propertyId] || 0;
      let newIndex;
      
      if (direction === 'next') {
        newIndex = current >= totalImages - 1 ? 0 : current + 1;
      } else {
        newIndex = current <= 0 ? totalImages - 1 : current - 1;
      }
      
      return { ...prev, [propertyId]: newIndex };
    });
  };

  // Toggle favorite
  const toggleFavorite = (propertyId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(propertyId)) {
        newFavorites.delete(propertyId);
      } else {
        newFavorites.add(propertyId);
      }
      return newFavorites;
    });
  };

  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Get property type badge color
  const getPropertyTypeBadge = (type: string) => {
    const badges = {
      putra: { label: 'Putra', color: 'bg-blue-100 text-blue-800' },
      putri: { label: 'Putri', color: 'bg-pink-100 text-pink-800' },
      campur: { label: 'Campur', color: 'bg-green-100 text-green-800' }
    };
    return badges[type as keyof typeof badges] || badges.campur;
  };

  if (loading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white rounded-2xl shadow-lg overflow-hidden animate-pulse">
                <div className="h-64 bg-gray-200"></div>
                <div className="p-6">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded w-full"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 via-white to-purple-50 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute top-0 left-0 w-72 h-72 bg-purple-200/30 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-pink-200/20 rounded-full blur-3xl translate-x-1/2 translate-y-1/2"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Modern section header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full px-6 py-2 mb-6">
            <span className="text-purple-600 font-semibold text-sm">✨ PILIHAN TERBAIK</span>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-black text-gray-900 mb-6 leading-tight">
            Kost <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600">Pilihan</span>
            <br />
            <span className="text-3xl md:text-4xl lg:text-5xl font-light italic">Terbaik ⭐</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Temukan kost berkualitas dengan fasilitas lengkap dan lokasi strategis yang sudah dipilih khusus untuk kamu
          </p>
        </div>

        {/* Modern filter toggle */}
        <div className="flex flex-col sm:flex-row justify-between items-center mb-12 space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-3">
            <div className="bg-gradient-to-r from-green-400 to-blue-400 rounded-full p-2">
              <Eye className="w-4 h-4 text-white" />
            </div>
            <span className="text-gray-700 font-medium">
              Menampilkan <span className="font-bold text-purple-600">{properties.length}</span> properti unggulan
            </span>
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-3 px-6 py-3 bg-white/80 backdrop-blur-sm border-2 border-purple-200 rounded-2xl hover:bg-purple-50 hover:border-purple-300 transition-all duration-300 shadow-lg hover:shadow-xl group"
          >
            <SlidersHorizontal className="w-5 h-5 text-purple-600 group-hover:rotate-180 transition-transform duration-300" />
            <span className="font-semibold text-purple-700">Filter</span>
          </button>
        </div>

        {/* Modern properties grid with staggered layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
          {properties.map((property, index) => {
            const currentImage = currentImageIndex[property.id] || 0;
            const propertyTypeBadge = getPropertyTypeBadge(property.property_type);
            const isFavorite = favorites.has(property.id);

            return (
              <div
                key={property.id}
                className={`group relative bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.03] hover:-rotate-1 ${
                  index % 3 === 1 ? 'lg:mt-8' : index % 3 === 2 ? 'lg:-mt-4' : ''
                } border border-white/50`}
                style={{
                  background: `linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%)`
                }}
              >
                {/* Modern image carousel */}
                <div className="relative h-72 overflow-hidden rounded-t-3xl">
                  {property.images.length > 0 && (
                    <>
                      <img
                        src={property.images[currentImage]?.url}
                        alt={property.images[currentImage]?.alt}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                      />

                      {/* Gradient overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

                      {/* Modern image navigation */}
                      {property.images.length > 1 && (
                        <>
                          <button
                            onClick={() => handleImageNavigation(property.id, 'prev', property.images.length)}
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-md text-white p-3 rounded-full hover:bg-white/30 transition-all duration-300 opacity-0 group-hover:opacity-100"
                          >
                            <ChevronLeft className="w-5 h-5" />
                          </button>
                          <button
                            onClick={() => handleImageNavigation(property.id, 'next', property.images.length)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-md text-white p-3 rounded-full hover:bg-white/30 transition-all duration-300 opacity-0 group-hover:opacity-100"
                          >
                            <ChevronRight className="w-5 h-5" />
                          </button>

                          {/* Modern image indicators */}
                          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                            {property.images.map((_, index) => (
                              <div
                                key={index}
                                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                                  index === currentImage ? 'bg-white w-6' : 'bg-white/60'
                                }`}
                              />
                            ))}
                          </div>
                        </>
                      )}
                    </>
                  )}

                  {/* Modern badges */}
                  <div className="absolute top-4 left-4 flex flex-col space-y-2">
                    <span className={`px-3 py-1.5 rounded-full text-xs font-bold backdrop-blur-md border border-white/30 ${propertyTypeBadge.color}`}>
                      {propertyTypeBadge.label}
                    </span>
                    {property.is_verified && (
                      <span className="px-3 py-1.5 bg-gradient-to-r from-green-400 to-emerald-500 text-white rounded-full text-xs font-bold flex items-center space-x-1 shadow-lg">
                        <Shield className="w-3 h-3" />
                        <span>Verified ✓</span>
                      </span>
                    )}
                  </div>

                  {/* Modern favorite button */}
                  <button
                    onClick={() => toggleFavorite(property.id)}
                    className="absolute top-4 right-4 p-3 bg-white/20 backdrop-blur-md rounded-full hover:bg-white/30 transition-all duration-300 group"
                  >
                    <Heart className={`w-5 h-5 transition-all duration-300 ${
                      isFavorite
                        ? 'fill-red-500 text-red-500 scale-110'
                        : 'text-white group-hover:text-red-400 group-hover:scale-110'
                    }`} />
                  </button>

                  {/* Modern availability indicator */}
                  <div className="absolute bottom-4 right-4">
                    <AvailabilityIndicator
                      propertyId={property.id}
                      showDetails={false}
                      className="bg-white/20 backdrop-blur-md text-white rounded-full border border-white/30"
                    />
                  </div>
                </div>

                {/* Modern property details */}
                <div className="p-6 space-y-4">
                  {/* Title and rating */}
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-purple-700 transition-colors duration-300">
                      {property.title}
                    </h3>
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full px-3 py-1">
                        <Star className="w-4 h-4 fill-white text-white" />
                        <span className="text-sm font-bold text-white">{property.rating}</span>
                      </div>
                      <span className="text-sm text-gray-600 font-medium">({property.review_count} ulasan)</span>
                    </div>
                  </div>

                  {/* Location */}
                  <div className="flex items-center space-x-2 text-gray-600">
                    <div className="bg-purple-100 rounded-full p-1.5">
                      <MapPin className="w-4 h-4 text-purple-600" />
                    </div>
                    <span className="text-sm font-medium">{property.location.district}, {property.location.city}</span>
                  </div>

                  {/* Modern amenities */}
                  <div className="flex flex-wrap gap-2">
                    {property.amenities.slice(0, 3).map((amenity) => (
                      <div key={amenity.id} className="flex items-center space-x-1.5 bg-gradient-to-r from-blue-50 to-purple-50 border border-purple-100 px-3 py-1.5 rounded-full text-xs font-medium text-purple-700">
                        <Wifi className="w-3 h-3" />
                        <span>{amenity.name}</span>
                      </div>
                    ))}
                    {property.amenities.length > 3 && (
                      <span className="text-xs text-purple-500 font-medium bg-purple-50 px-2 py-1 rounded-full">
                        +{property.amenities.length - 3} lainnya
                      </span>
                    )}
                  </div>

                  {/* Modern price display */}
                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-4 border border-purple-100">
                    <div className="text-2xl font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600">
                      {formatPrice(property.price_monthly)}
                    </div>
                    <div className="text-sm text-gray-600 font-medium">per bulan • All inclusive</div>
                  </div>

                  {/* Modern action buttons */}
                  <div className="flex space-x-3 pt-2">
                    <button
                      onClick={() => onPropertyClick?.(property)}
                      className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-3 px-4 rounded-2xl transition-all duration-300 flex items-center justify-center space-x-2 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105"
                    >
                      <Eye className="w-5 h-5" />
                      <span>Lihat Detail</span>
                    </button>
                    <button
                      onClick={() => onContactOwner?.(property)}
                      className="bg-gradient-to-r from-green-400 to-emerald-500 hover:from-green-500 hover:to-emerald-600 text-white p-3 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                    >
                      <MessageCircle className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Modern view more button */}
        <div className="text-center mt-16">
          <button className="group bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-12 py-4 rounded-2xl font-bold text-lg transition-all duration-300 shadow-2xl hover:shadow-purple-500/25 transform hover:scale-105 flex items-center space-x-3 mx-auto">
            <span>Lihat Semua Properti</span>
            <div className="bg-white/20 rounded-full p-1 group-hover:rotate-45 transition-transform duration-300">
              <ChevronRight className="w-5 h-5" />
            </div>
          </button>
          <p className="text-gray-600 mt-4 font-medium">Temukan lebih dari 2000+ kost berkualitas lainnya</p>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProperties;
