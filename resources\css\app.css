@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

:root {
    /* Gen-Z Light Mode - Warm, soft, and energetic palette */
    --background: oklch(0.98 0.008 85);          /* Soft warm white with subtle peach undertone */
    --foreground: oklch(0.15 0.02 25);           /* Deep warm charcoal */
    --card: oklch(0.995 0.005 75);               /* Pure warm white for cards */
    --card-foreground: oklch(0.15 0.02 25);      /* Deep warm charcoal */
    --popover: oklch(0.995 0.005 75);            /* Pure warm white */
    --popover-foreground: oklch(0.15 0.02 25);   /* Deep warm charcoal */
    --primary: oklch(0.65 0.15 25);              /* Warm coral/terracotta - Gen-Z favorite */
    --primary-foreground: oklch(0.98 0.008 85);  /* Soft warm white */
    --secondary: oklch(0.92 0.02 75);            /* Light warm beige */
    --secondary-foreground: oklch(0.25 0.03 35); /* Warm dark brown */
    --muted: oklch(0.94 0.015 80);               /* Very light warm gray */
    --muted-foreground: oklch(0.45 0.02 45);     /* Medium warm gray */
    --accent: oklch(0.75 0.12 45);               /* Soft peach accent */
    --accent-foreground: oklch(0.15 0.02 25);    /* Deep warm charcoal */
    --destructive: oklch(0.65 0.2 15);           /* Warm red */
    --destructive-foreground: oklch(0.98 0.008 85); /* Soft warm white */
    --border: oklch(0.88 0.02 70);               /* Light warm border */
    --input: oklch(0.92 0.015 75);               /* Light warm input background */
    --ring: oklch(0.65 0.15 25);                 /* Primary color for focus rings */
    --chart-1: oklch(0.65 0.15 25);              /* Coral */
    --chart-2: oklch(0.7 0.12 65);               /* Warm orange */
    --chart-3: oklch(0.6 0.1 95);                /* Warm yellow */
    --chart-4: oklch(0.55 0.08 145);             /* Sage green */
    --chart-5: oklch(0.6 0.1 185);               /* Soft teal */
    --radius: 0.625rem;
    --sidebar: oklch(0.995 0.005 75);            /* Pure warm white */
    --sidebar-foreground: oklch(0.15 0.02 25);   /* Deep warm charcoal */
    --sidebar-primary: oklch(0.65 0.15 25);      /* Warm coral */
    --sidebar-primary-foreground: oklch(0.98 0.008 85); /* Soft warm white */
    --sidebar-accent: oklch(0.92 0.02 75);       /* Light warm beige */
    --sidebar-accent-foreground: oklch(0.25 0.03 35); /* Warm dark brown */
    --sidebar-border: oklch(0.88 0.02 70);       /* Light warm border */
    --sidebar-ring: oklch(0.65 0.15 25);         /* Primary color */
}

.dark {
    /* Gen-Z Dark Mode - Deep, rich, sophisticated with vibrant accents */
    --background: oklch(0.12 0.015 285);         /* Deep navy-purple base */
    --foreground: oklch(0.95 0.01 75);           /* Warm off-white */
    --card: oklch(0.15 0.02 280);                /* Slightly lighter navy-purple for cards */
    --card-foreground: oklch(0.95 0.01 75);      /* Warm off-white */
    --popover: oklch(0.15 0.02 280);             /* Slightly lighter navy-purple */
    --popover-foreground: oklch(0.95 0.01 75);   /* Warm off-white */
    --primary: oklch(0.7 0.18 35);               /* Vibrant coral - Gen-Z energy */
    --primary-foreground: oklch(0.12 0.015 285); /* Deep navy-purple */
    --secondary: oklch(0.22 0.03 275);           /* Medium purple-gray */
    --secondary-foreground: oklch(0.9 0.015 75); /* Light warm gray */
    --muted: oklch(0.2 0.025 280);               /* Dark purple-gray */
    --muted-foreground: oklch(0.65 0.02 70);     /* Medium warm gray */
    --accent: oklch(0.75 0.15 55);               /* Bright peach accent */
    --accent-foreground: oklch(0.12 0.015 285);  /* Deep navy-purple */
    --destructive: oklch(0.65 0.22 20);          /* Vibrant red-orange */
    --destructive-foreground: oklch(0.95 0.01 75); /* Warm off-white */
    --border: oklch(0.25 0.03 275);              /* Medium purple-gray border */
    --input: oklch(0.18 0.025 280);              /* Dark purple input background */
    --ring: oklch(0.7 0.18 35);                  /* Primary coral for focus rings */
    --chart-1: oklch(0.7 0.18 35);               /* Vibrant coral */
    --chart-2: oklch(0.75 0.15 55);              /* Bright peach */
    --chart-3: oklch(0.8 0.12 85);               /* Warm yellow */
    --chart-4: oklch(0.65 0.1 155);              /* Mint green */
    --chart-5: oklch(0.7 0.12 195);              /* Cyan blue */
    --sidebar: oklch(0.1 0.02 285);              /* Darker navy-purple */
    --sidebar-foreground: oklch(0.95 0.01 75);   /* Warm off-white */
    --sidebar-primary: oklch(0.7 0.18 35);       /* Vibrant coral */
    --sidebar-primary-foreground: oklch(0.12 0.015 285); /* Deep navy-purple */
    --sidebar-accent: oklch(0.22 0.03 275);      /* Medium purple-gray */
    --sidebar-accent-foreground: oklch(0.9 0.015 75); /* Light warm gray */
    --sidebar-border: oklch(0.25 0.03 275);      /* Medium purple-gray border */
    --sidebar-ring: oklch(0.7 0.18 35);          /* Primary coral */
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

/* Custom animations for Gen-Z modern design */
@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
        transform: translate(0px, 0px) scale(1);
    }
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}
