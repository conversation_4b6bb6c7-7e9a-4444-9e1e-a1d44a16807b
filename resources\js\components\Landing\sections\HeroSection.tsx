import React, { useState } from 'react';
import { Search, MapPin, Users, Home, Star, ChevronDown } from 'lucide-react';

// Property type options
interface PropertyType {
  id: string;
  label: string;
  icon: React.ReactElement;
  description: string;
}

const propertyTypes: PropertyType[] = [
  {
    id: 'putra',
    label: 'Kost Putra',
    icon: <Users className="w-5 h-5" />,
    description: 'Khusus Laki-laki'
  },
  {
    id: 'putri',
    label: 'Kost Putri',
    icon: <Users className="w-5 h-5" />,
    description: 'Khusus Perempuan'
  },
  {
    id: 'campur',
    label: 'Kost Campur',
    icon: <Users className="w-5 h-5" />,
    description: '<PERSON><PERSON>-laki & Perempuan'
  }
];

// Hero section props interface
interface HeroSectionProps {
  onSearch?: (query: string, propertyType: string) => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onSearch }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPropertyType, setSelectedPropertyType] = useState('');
  const [isPropertyTypeOpen, setIsPropertyTypeOpen] = useState(false);

  // Handle search submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch) {
      onSearch(searchQuery, selectedPropertyType);
    }
  };

  // Handle property type selection
  const handlePropertyTypeSelect = (type: PropertyType) => {
    setSelectedPropertyType(type.id);
    setIsPropertyTypeOpen(false);
  };

  // Get selected property type label
  const getSelectedPropertyTypeLabel = () => {
    const selected = propertyTypes.find(type => type.id === selectedPropertyType);
    return selected ? selected.label : 'Pilih Tipe Kost';
  };

  return (
    <section className="relative min-h-screen overflow-hidden">
      {/* Modern gradient background with mesh pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-800">
        {/* Animated gradient mesh */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
          <div className="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
          <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
        </div>

        {/* Modern geometric shapes */}
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-white/40 rounded-full animate-ping"></div>
        <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-yellow-400/60 rounded-full animate-ping animation-delay-1000"></div>
        <div className="absolute bottom-1/4 right-1/4 w-3 h-3 bg-pink-400/40 rounded-full animate-ping animation-delay-2000"></div>

        {/* Grid overlay */}
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M40 40H0V0h40v40zM20 20H0v20h20V20zm20-20H20v20h20V0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}
        ></div>
      </div>

      {/* Main content with modern grid layout */}
      <div className="relative z-10 min-h-screen">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-12 gap-8 items-center min-h-screen py-20">

            {/* Left content area */}
            <div className="lg:col-span-7 space-y-8">
              {/* Trust indicators - modern pill design */}
              <div className="flex flex-wrap gap-3 justify-center lg:justify-start">
                <div className="bg-white/10 backdrop-blur-md rounded-full px-4 py-2 border border-white/20">
                  <div className="flex items-center space-x-2 text-white">
                    <Home className="w-4 h-4" />
                    <span className="text-sm font-semibold">2K+ Kost</span>
                  </div>
                </div>
                <div className="bg-white/10 backdrop-blur-md rounded-full px-4 py-2 border border-white/20">
                  <div className="flex items-center space-x-2 text-white">
                    <Users className="w-4 h-4" />
                    <span className="text-sm font-semibold">50K+ Penghuni</span>
                  </div>
                </div>
                <div className="bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full px-4 py-2">
                  <div className="flex items-center space-x-2 text-black">
                    <Star className="w-4 h-4 fill-current" />
                    <span className="text-sm font-bold">4.8 Rating</span>
                  </div>
                </div>
              </div>

              {/* Modern headline with mixed typography */}
              <div className="text-left space-y-6">
                <div className="space-y-2">
                  <h1 className="text-5xl md:text-7xl lg:text-8xl font-black text-white leading-none tracking-tight">
                    Temukan
                  </h1>
                  <div className="flex items-center space-x-4">
                    <h1 className="text-5xl md:text-7xl lg:text-8xl font-black text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 via-pink-400 to-purple-400 leading-none">
                      Kost
                    </h1>
                    <div className="hidden lg:block w-16 h-1 bg-gradient-to-r from-yellow-400 to-pink-400 rounded-full"></div>
                  </div>
                  <h1 className="text-4xl md:text-6xl lg:text-7xl font-light text-white/90 leading-none italic">
                    Impianmu ✨
                  </h1>
                </div>

                <div className="space-y-4 max-w-lg">
                  <p className="text-xl md:text-2xl text-white/90 font-medium leading-relaxed">
                    Platform <span className="text-yellow-400 font-bold">terpercaya</span> untuk mencari kost berkualitas
                  </p>
                  <p className="text-lg text-white/70 leading-relaxed">
                    Ribuan pilihan kost di seluruh Indonesia dengan harga transparan dan lokasi strategis
                  </p>
                </div>
              </div>

              {/* Modern search form */}
              <div className="mt-8">
                <form onSubmit={handleSearch} className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 shadow-2xl">
                  <div className="space-y-4">
                    {/* Location search */}
                    <div>
                      <label htmlFor="location" className="block text-sm font-semibold text-white/90 mb-3">
                        📍 Lokasi atau Area
                      </label>
                      <div className="relative">
                        <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5" />
                        <input
                          id="location"
                          type="text"
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          placeholder="Cari berdasarkan kota, kampus, atau alamat..."
                          className="w-full pl-12 pr-4 py-4 bg-white/20 backdrop-blur-sm border border-white/30 rounded-2xl focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 text-white placeholder-white/60 transition-all duration-300"
                        />
                      </div>
                    </div>

                    {/* Property type selector */}
                    <div>
                      <label htmlFor="property-type" className="block text-sm font-semibold text-white/90 mb-3">
                        🏠 Tipe Kost
                      </label>
                      <div className="relative">
                        <button
                          type="button"
                          onClick={() => setIsPropertyTypeOpen(!isPropertyTypeOpen)}
                          className="w-full px-4 py-4 bg-white/20 backdrop-blur-sm border border-white/30 rounded-2xl text-left focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 flex items-center justify-between transition-all duration-300 hover:bg-white/30"
                        >
                          <span className={selectedPropertyType ? 'text-white font-medium' : 'text-white/60'}>
                            {getSelectedPropertyTypeLabel()}
                          </span>
                          <ChevronDown className={`w-5 h-5 text-white/60 transition-transform duration-300 ${isPropertyTypeOpen ? 'rotate-180' : ''}`} />
                        </button>

                        {/* Modern dropdown menu */}
                        {isPropertyTypeOpen && (
                          <div className="absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl z-20 overflow-hidden">
                            {propertyTypes.map((type) => (
                              <button
                                key={type.id}
                                type="button"
                                onClick={() => handlePropertyTypeSelect(type)}
                                className="w-full px-6 py-4 text-left hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 flex items-center space-x-3 transition-all duration-300 group"
                              >
                                <div className="text-purple-600 group-hover:text-purple-700 transition-colors">
                                  {type.icon}
                                </div>
                                <div>
                                  <div className="font-semibold text-gray-900 group-hover:text-purple-900">{type.label}</div>
                                  <div className="text-sm text-gray-600 group-hover:text-purple-700">{type.description}</div>
                                </div>
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Modern search button */}
                    <div className="pt-2">
                      <button
                        type="submit"
                        className="w-full bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 text-black font-bold py-4 px-8 rounded-2xl transition-all duration-300 flex items-center justify-center space-x-3 shadow-2xl hover:shadow-yellow-400/25 hover:scale-105 transform"
                      >
                        <Search className="w-6 h-6" />
                        <span className="text-lg">Cari Kost</span>
                      </button>
                    </div>

                    {/* Quick filters */}
                    <div className="mt-6 pt-6 border-t border-white/20">
                      <p className="text-sm text-white/80 mb-4 font-medium">🔥 Pencarian populer:</p>
                      <div className="flex flex-wrap gap-2">
                        {[
                          'Kost dekat UI',
                          'Jakarta Selatan',
                          'Kost murah Depok',
                          'Bandung',
                          'dekat ITB',
                          'Yogyakarta'
                        ].map((tag) => (
                          <button
                            key={tag}
                            type="button"
                            onClick={() => setSearchQuery(tag)}
                            className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white/90 text-sm rounded-full transition-all duration-300 border border-white/20 hover:border-white/40 backdrop-blur-sm"
                          >
                            {tag}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>

              {/* Right side content - Visual elements */}
              <div className="lg:col-span-5 relative">
                <div className="relative h-96 lg:h-full">
                  {/* Floating cards */}
                  <div className="absolute top-0 right-0 bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-blue-400 rounded-full flex items-center justify-center">
                        <Home className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <div className="text-white font-bold text-lg">2000+</div>
                        <div className="text-white/70 text-sm">Kost Tersedia</div>
                      </div>
                    </div>
                  </div>

                  <div className="absolute top-32 left-0 bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 transform -rotate-3 hover:rotate-0 transition-transform duration-500">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
                        <Users className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <div className="text-white font-bold text-lg">50K+</div>
                        <div className="text-white/70 text-sm">Penghuni Bahagia</div>
                      </div>
                    </div>
                  </div>

                  <div className="absolute bottom-0 right-8 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-3xl p-6 transform rotate-2 hover:rotate-0 transition-transform duration-500">
                    <div className="flex items-center space-x-3">
                      <Star className="w-8 h-8 text-black fill-current" />
                      <div>
                        <div className="text-black font-bold text-xl">4.8</div>
                        <div className="text-black/70 text-sm font-medium">Rating</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 animate-bounce">
        <div className="flex flex-col items-center space-y-2">
          <span className="text-sm">Scroll untuk melihat lebih banyak</span>
          <ChevronDown className="w-5 h-5" />
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
