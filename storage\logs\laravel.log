[2025-07-11 19:23:05] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:23:06] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:25:25] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:25:26] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:25:44] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:25:44] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:25:55] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:25:55] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:26:06] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:26:06] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:26:23] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:26:23] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:26:40] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:26:41] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:26:54] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:26:55] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:27:20] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:27:21] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:28:51] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:28:51] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:30:09] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:30:09] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:44:36] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:44:36] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 03:48:51] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 03:48:52] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
