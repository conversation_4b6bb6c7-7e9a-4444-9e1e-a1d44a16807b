import React, { useState } from 'react';
import {
  Search,
  Calendar,
  MessageCircle,
  Phone,
  Mail,
  Clock,
  CheckCircle,
  ArrowRight,
  Users,
  Shield,
  Star,
  X
} from 'lucide-react';

// Contact form interface
interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  message: string;
  preferredContact: 'email' | 'phone' | 'whatsapp';
  interestedIn: string;
}

interface CTASectionProps {
  onSearchClick?: () => void;
  onScheduleVisit?: (data: ContactFormData) => void;
  onContactSubmit?: (data: ContactFormData) => void;
}

const CTASection: React.FC<CTASectionProps> = ({
  onSearchClick,
  onContactSubmit
}) => {
  const [showContactForm, setShowContactForm] = useState(false);
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    phone: '',
    message: '',
    preferredContact: 'whatsapp',
    interestedIn: 'general'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (onContactSubmit) {
        onContactSubmit(formData);
      }
      
      setSubmitSuccess(true);
      setTimeout(() => {
        setShowContactForm(false);
        setSubmitSuccess(false);
        setFormData({
          name: '',
          email: '',
          phone: '',
          message: '',
          preferredContact: 'whatsapp',
          interestedIn: 'general'
        });
      }, 2000);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // WhatsApp contact handler
  const handleWhatsAppContact = () => {
    const message = encodeURIComponent(
      'Halo! Saya tertarik untuk mencari kost melalui platform Satu Atap. Bisakah Anda membantu saya?'
    );
    const whatsappUrl = `https://wa.me/6281234567890?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <section className="py-16 relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #27374D 0%, #526D82 50%, #9DB2BF 100%)' }}>
      {/* Background decorations */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-64 h-64 rounded-full -translate-x-32 -translate-y-32" style={{ backgroundColor: 'rgba(221, 230, 237, 0.1)' }}></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 rounded-full translate-x-48 translate-y-48" style={{ backgroundColor: 'rgba(221, 230, 237, 0.1)' }}></div>
        <div className="absolute top-1/2 left-1/4 w-32 h-32 rounded-full blur-xl" style={{ backgroundColor: 'rgba(221, 230, 237, 0.2)' }}></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Main CTA */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6" style={{ color: '#DDE6ED' }}>
            Siap Menemukan Kost
            <span className="block text-transparent bg-clip-text" style={{ background: 'linear-gradient(45deg, #9DB2BF, #DDE6ED)', WebkitBackgroundClip: 'text' }}>
              Impianmu?
            </span>
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto" style={{ color: 'rgba(221, 230, 237, 0.9)' }}>
            Bergabunglah dengan ribuan penghuni yang telah menemukan kost terbaik melalui platform kami
          </p>

          {/* Primary CTAs */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <button
              onClick={onSearchClick}
              className="px-8 py-4 rounded-xl font-semibold text-lg hover:opacity-90 transition-all shadow-xl hover:shadow-2xl transform hover:scale-105 flex items-center space-x-3"
              style={{
                backgroundColor: '#DDE6ED',
                color: '#27374D'
              }}
            >
              <Search className="w-6 h-6" />
              <span>Cari Kost Sekarang</span>
              <ArrowRight className="w-5 h-5" />
            </button>

            <button
              onClick={() => setShowContactForm(true)}
              className="bg-transparent border-2 px-8 py-4 rounded-xl font-semibold text-lg hover:opacity-90 transition-colors flex items-center space-x-3"
              style={{
                borderColor: '#DDE6ED',
                color: '#DDE6ED'
              }}
            >
              <Calendar className="w-6 h-6" />
              <span>Jadwalkan Kunjungan</span>
            </button>
          </div>

          {/* Quick contact options */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button
              onClick={handleWhatsAppContact}
              className="px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2"
              style={{
                backgroundColor: '#9DB2BF',
                color: '#27374D'
              }}
            >
              <MessageCircle className="w-5 h-5" />
              <span>Chat WhatsApp</span>
            </button>

            <a
              href="tel:+6281234567890"
              className="px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2"
              style={{
                backgroundColor: '#526D82',
                color: '#DDE6ED'
              }}
            >
              <Phone className="w-5 h-5" />
              <span>Telepon Langsung</span>
            </a>
          </div>
        </div>

        {/* Trust indicators */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="text-center" style={{ color: '#DDE6ED' }}>
            <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style={{ backgroundColor: 'rgba(221, 230, 237, 0.2)' }}>
              <Shield className="w-8 h-8" />
            </div>
            <h3 className="text-xl font-semibold mb-2">100% Aman</h3>
            <p style={{ color: 'rgba(221, 230, 237, 0.8)' }}>Semua properti telah diverifikasi dan terjamin keamanannya</p>
          </div>

          <div className="text-center" style={{ color: '#DDE6ED' }}>
            <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style={{ backgroundColor: 'rgba(221, 230, 237, 0.2)' }}>
              <Users className="w-8 h-8" />
            </div>
            <h3 className="text-xl font-semibold mb-2">50K+ Penghuni</h3>
            <p style={{ color: 'rgba(221, 230, 237, 0.8)' }}>Bergabung dengan komunitas penghuni kost terbesar</p>
          </div>

          <div className="text-center" style={{ color: '#DDE6ED' }}>
            <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style={{ backgroundColor: 'rgba(221, 230, 237, 0.2)' }}>
              <Star className="w-8 h-8" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Rating 4.8/5</h3>
            <p style={{ color: 'rgba(221, 230, 237, 0.8)' }}>Kepuasan pengguna adalah prioritas utama kami</p>
          </div>
        </div>

        {/* Contact info */}
        <div className="text-center" style={{ color: 'rgba(221, 230, 237, 0.8)' }}>
          <p className="mb-4">Butuh bantuan? Tim customer service kami siap membantu 24/7</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center text-sm">
            <div className="flex items-center space-x-2">
              <Mail className="w-4 h-4" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center space-x-2">
              <Phone className="w-4 h-4" />
              <span>+62 812-3456-7890</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4" />
              <span>24/7 Customer Support</span>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Form Modal */}
      {showContactForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto" style={{ backgroundColor: '#DDE6ED' }}>
            {submitSuccess ? (
              // Success state
              <div className="p-8 text-center">
                <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style={{ backgroundColor: '#9DB2BF' }}>
                  <CheckCircle className="w-8 h-8" style={{ color: '#27374D' }} />
                </div>
                <h3 className="text-xl font-semibold mb-2" style={{ color: '#27374D' }}>
                  Pesan Terkirim!
                </h3>
                <p className="mb-4" style={{ color: '#526D82' }}>
                  Terima kasih! Tim kami akan menghubungi Anda dalam 24 jam.
                </p>
                <div className="text-sm" style={{ color: '#526D82' }}>
                  Jendela ini akan tertutup otomatis...
                </div>
              </div>
            ) : (
              // Form state
              <>
                <div className="flex items-center justify-between p-6 border-b" style={{ borderColor: '#9DB2BF' }}>
                  <h3 className="text-xl font-semibold" style={{ color: '#27374D' }}>
                    Jadwalkan Kunjungan
                  </h3>
                  <button
                    onClick={() => setShowContactForm(false)}
                    className="hover:opacity-70"
                    style={{ color: '#526D82' }}
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="p-6 space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      Nama Lengkap *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Masukkan nama lengkap"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      Email *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                      Nomor WhatsApp *
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="08123456789"
                    />
                  </div>

                  <div>
                    <label htmlFor="interestedIn" className="block text-sm font-medium text-gray-700 mb-1">
                      Tertarik Dengan
                    </label>
                    <select
                      id="interestedIn"
                      name="interestedIn"
                      value={formData.interestedIn}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="general">Informasi Umum</option>
                      <option value="specific-property">Properti Tertentu</option>
                      <option value="area-recommendation">Rekomendasi Area</option>
                      <option value="pricing">Informasi Harga</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                      Pesan (Opsional)
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Ceritakan kebutuhan kost Anda..."
                    />
                  </div>

                  <div className="flex space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowContactForm(false)}
                      className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      Batal
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          <span>Mengirim...</span>
                        </>
                      ) : (
                        <>
                          <Calendar className="w-4 h-4" />
                          <span>Kirim Permintaan</span>
                        </>
                      )}
                    </button>
                  </div>
                </form>
              </>
            )}
          </div>
        </div>
      )}
    </section>
  );
};

export default CTASection;
