---
type: "always_apply"
---

description: Best practices and guidelines for Chrome Extension development
globs: **/*.{ts,tsx,js,jsx}
---

- Write clear, modular TypeScript code with proper type definitions
- Follow functional programming patterns; avoid classes
- Structure files logically: popup, background, content scripts, utils
- Implement proper error handling and logging
- Document code with JSDoc comments